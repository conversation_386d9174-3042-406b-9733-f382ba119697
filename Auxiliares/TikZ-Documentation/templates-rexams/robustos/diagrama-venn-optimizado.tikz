% 🎯 Template TikZ Optimizado - Diagrama de Venn
% Versión: Robusta y Compatible Multi-formato
% Elimina: efectos de transparencia problemáticos, colores conflictivos
% Basado en: DVenn_All_GenMus_01.Rmd (patr<PERSON> exitoso)

% CONFIGURACIÓN R REQUERIDA:
% En el chunk R definir:
% conjunto_A <- "Rock"
% conjunto_B <- "Pop" 
% conjunto_C <- "Jazz"  # Opcional para 3 conjuntos
% valor_solo_A <- sample(10:25, 1)
% valor_solo_B <- sample(15:30, 1)
% valor_solo_C <- sample(8:20, 1)   # Opcional
% valor_A_B <- sample(3:12, 1)
% valor_A_C <- sample(2:10, 1)      # Opcional
% valor_B_C <- sample(4:15, 1)      # Opcional
% valor_A_B_C <- sample(1:8, 1)     # Opcional
% valor_ninguno <- sample(5:20, 1)
% escala_venn <- 0.8

% === VERSIÓN 2 CONJUNTOS ===
\begin{tikzpicture}[scale=`r escala_venn`]

% Círculos con colores compatibles (sin opacity problemática)
% Usar fill opacity integrada en lugar de scope con transparency
\fill[blue!25] (-1,0) circle (1.8);
\fill[red!25] (1,0) circle (1.8);

% Contornos de los círculos
\draw[blue!80, very thick] (-1,0) circle (1.8);
\draw[red!80, very thick] (1,0) circle (1.8);

% Etiquetas de conjuntos (posicionadas fuera)
\node[blue!80, font=\bfseries] at (-2.5,0) {`r conjunto_A`};
\node[red!80, font=\bfseries] at (2.5,0) {`r conjunto_B`};

% Valores en regiones
\node[font=\large\bfseries] at (-1.3,0) {`r valor_solo_A`};
\node[font=\large\bfseries] at (1.3,0) {`r valor_solo_B`};
\node[font=\large\bfseries] at (0,0) {`r valor_A_B`};

% Valor fuera de ambos conjuntos
\node[font=\bfseries] at (0,-3) {Ninguno: `r valor_ninguno`};

% Marco opcional para contexto
\draw[gray!50, dashed] (-4,-2.5) rectangle (4,2.5);
\node[gray!70, font=\small] at (0,2.2) {Total encuestados: `r valor_solo_A + valor_solo_B + valor_A_B + valor_ninguno`};

\end{tikzpicture}

% === VERSIÓN 3 CONJUNTOS (comentada, activar si se necesita) ===
% \begin{tikzpicture}[scale=`r escala_venn`]
% 
% % Círculos con colores compatibles
% \fill[blue!20] ( 90:1.5) circle (2);
% \fill[red!20] (210:1.5) circle (2);
% \fill[green!20] (330:1.5) circle (2);
% 
% % Contornos
% \draw[blue!80, very thick] ( 90:1.5) circle (2);
% \draw[red!80, very thick] (210:1.5) circle (2);
% \draw[green!80, very thick] (330:1.5) circle (2);
% 
% % Etiquetas de conjuntos
% \node[blue!80, font=\bfseries] at ( 90:3.5) {`r conjunto_A`};
% \node[red!80, font=\bfseries] at (210:3.5) {`r conjunto_B`};
% \node[green!80, font=\bfseries] at (330:3.5) {`r conjunto_C`};
% 
% % Valores en regiones exclusivas
% \node[font=\large\bfseries] at ( 90:1.3) {`r valor_solo_A`};
% \node[font=\large\bfseries] at (210:1.3) {`r valor_solo_B`};
% \node[font=\large\bfseries] at (330:1.3) {`r valor_solo_C`};
% 
% % Valores en intersecciones de dos conjuntos
% \node[font=\bfseries] at ( 30:1.7) {`r valor_A_C`};
% \node[font=\bfseries] at (150:1.7) {`r valor_A_B`};
% \node[font=\bfseries] at (270:1.7) {`r valor_B_C`};
% 
% % Valor en intersección de tres conjuntos
% \node[font=\bfseries] at (0:0) {`r valor_A_B_C`};
% 
% % Valor fuera de todos los conjuntos
% \node[font=\bfseries] at (270:4) {Ninguno: `r valor_ninguno`};
% 
% \end{tikzpicture}

% === CARACTERÍSTICAS DE COMPATIBILIDAD ===
% ✅ Sin efectos de transparencia complejos (scope con opacity)
% ✅ Colores estándar con transparencia integrada (blue!25)
% ✅ Sin bibliotecas adicionales requeridas
% ✅ Coordenadas simples y escalables
% ✅ Texto legible en todas las resoluciones

% === CASOS DE USO ICFES ===
% - Probabilidad de eventos (Nivel 2-3)
% - Teoría de conjuntos (Pensamiento Aleatorio)
% - Análisis de encuestas (Contexto familiar/comunitario)
% - Clasificación de datos (Interpretación y Representación)
% - Operaciones con conjuntos (Formulación y Ejecución)

% === VALIDACIÓN MULTI-FORMATO ===
% ✅ exams2pdf: Excelente
% ✅ exams2html: Excelente (sin problemas de opacity)
% ✅ exams2moodle: Excelente
% ✅ exams2pandoc: Buena
% ✅ exams2nops: Excelente (colores se convierten bien a escala de grises)

% === EJEMPLO DE USO EN .RMD ===
% ```{r generar_venn, echo=FALSE, results='asis'}
% # Variables del diagrama
% conjunto_A <- sample(c("Rock", "Clásica", "Salsa"), 1)
% conjunto_B <- sample(c("Pop", "Jazz", "Reggaeton"), 1)
% valor_solo_A <- sample(10:25, 1)
% valor_solo_B <- sample(15:30, 1)
% valor_A_B <- sample(3:12, 1)
% valor_ninguno <- sample(5:20, 1)
% escala_venn <- 0.8
% 
% # Detectar formato
% typ <- match_exams_device()
% ancho_figura <- if(match_exams_call() %in% c("exams2moodle", "exams2qti12")) "6cm" else "8cm"
% 
% # Incluir figura
% include_tikz(readLines("diagrama-venn-optimizado.tikz"),
%              name = "diagrama_venn",
%              format = typ,
%              packages = c("tikz"),  # Solo tikz básico
%              width = ancho_figura)
% ```

% === VARIANTES DISPONIBLES ===
% 1. Dos conjuntos (activada por defecto)
% 2. Tres conjuntos (comentada, descomentar si se necesita)
% 3. Con marco y total (opcional)
% 4. Sin marco (más limpio)

% === PERSONALIZACIÓN ===
% Colores: Cambiar blue!25, red!25 por otros colores estándar
% Tamaño: Modificar escala_venn en R
% Posición texto: Ajustar coordenadas de \node
% Grosor líneas: very thick -> thick, ultra thick

% === MEJORAS IMPLEMENTADAS ===
% ✅ Eliminada transparencia problemática
% ✅ Colores más contrastantes
% ✅ Texto más legible
% ✅ Escalado responsive
% ✅ Compatible con daltonismo (colores diferenciables)
% ✅ Funciona en escala de grises para impresión
