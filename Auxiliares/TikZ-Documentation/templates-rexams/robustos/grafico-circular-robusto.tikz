% 🎯 Template TikZ Robusto - Gráfico Circular
% Versión: Compatible Multi-formato y Escalable
% Elimina: cálculos complejos, bibliotecas problemáticas
% Optimizado para: ICFES Estadística y Probabilidad

% CONFIGURACIÓN R REQUERIDA:
% En el chunk R definir:
% categorias <- c("Categoría A", "Categoría B", "Categoría C", "Categoría D")
% valores <- c(30, 25, 25, 20)  # Porcentajes que sumen 100
% colores <- c("blue", "red", "green", "orange")  # Colores estándar
% intensidades <- c(60, 60, 60, 60)  # Intensidad de colores (30-80)
% radio_grafico <- 2.5
% mostrar_porcentajes <- TRUE
% mostrar_leyenda <- TRUE

% === GRÁFICO CIRCULAR BÁSICO ===
\begin{tikzpicture}[scale=0.8]

% Centro del gráfico
\coordinate (centro) at (0,0);

% Calcular ángulos acumulativos (usando variables R)
% En R: angulos_acum <- cumsum(valores * 3.6)  # Convertir % a grados
% angulo_inicio_A <- 0
% angulo_fin_A <- valores[1] * 3.6
% angulo_inicio_B <- angulo_fin_A  
% angulo_fin_B <- angulo_fin_A + valores[2] * 3.6
% etc.

% === SECTORES DEL GRÁFICO ===
% Sector A
\fill[`r colores[1]`!`r intensidades[1]`] (centro) 
  -- (`r 0`:`r radio_grafico`) 
  arc (`r 0`:`r valores[1] * 3.6`:`r radio_grafico`) 
  -- cycle;

% Sector B  
\fill[`r colores[2]`!`r intensidades[2]`] (centro) 
  -- (`r valores[1] * 3.6`:`r radio_grafico`) 
  arc (`r valores[1] * 3.6`:`r sum(valores[1:2]) * 3.6`:`r radio_grafico`) 
  -- cycle;

% Sector C
\fill[`r colores[3]`!`r intensidades[3]`] (centro) 
  -- (`r sum(valores[1:2]) * 3.6`:`r radio_grafico`) 
  arc (`r sum(valores[1:2]) * 3.6`:`r sum(valores[1:3]) * 3.6`:`r radio_grafico`) 
  -- cycle;

% Sector D
\fill[`r colores[4]`!`r intensidades[4]`] (centro) 
  -- (`r sum(valores[1:3]) * 3.6`:`r radio_grafico`) 
  arc (`r sum(valores[1:3]) * 3.6`:`r 360`:`r radio_grafico`) 
  -- cycle;

% === CONTORNOS ===
% Círculo exterior
\draw[black, thick] (centro) circle (`r radio_grafico`);

% Líneas divisorias
\draw[black] (centro) -- (`r 0`:`r radio_grafico`);
\draw[black] (centro) -- (`r valores[1] * 3.6`:`r radio_grafico`);
\draw[black] (centro) -- (`r sum(valores[1:2]) * 3.6`:`r radio_grafico`);
\draw[black] (centro) -- (`r sum(valores[1:3]) * 3.6`:`r radio_grafico`);

% === ETIQUETAS DE PORCENTAJES (condicional) ===
% Solo si mostrar_porcentajes es TRUE
% Posiciones calculadas en el centro de cada sector
\node[font=\bfseries] at (`r valores[1] * 3.6 / 2`:`r radio_grafico * 0.7`) {`r valores[1]`\%};
\node[font=\bfseries] at (`r valores[1] * 3.6 + valores[2] * 3.6 / 2`:`r radio_grafico * 0.7`) {`r valores[2]`\%};
\node[font=\bfseries] at (`r sum(valores[1:2]) * 3.6 + valores[3] * 3.6 / 2`:`r radio_grafico * 0.7`) {`r valores[3]`\%};
\node[font=\bfseries] at (`r sum(valores[1:3]) * 3.6 + valores[4] * 3.6 / 2`:`r radio_grafico * 0.7`) {`r valores[4]`\%};

% === LEYENDA (condicional) ===
% Solo si mostrar_leyenda es TRUE
% Posicionada a la derecha del gráfico
\fill[`r colores[1]`!`r intensidades[1]`] (`r radio_grafico + 1`, 1.5) rectangle ++(0.4, 0.3);
\node[right] at (`r radio_grafico + 1.5`, 1.65) {\small `r categorias[1]`};

\fill[`r colores[2]`!`r intensidades[2]`] (`r radio_grafico + 1`, 1.0) rectangle ++(0.4, 0.3);
\node[right] at (`r radio_grafico + 1.5`, 1.15) {\small `r categorias[2]`};

\fill[`r colores[3]`!`r intensidades[3]`] (`r radio_grafico + 1`, 0.5) rectangle ++(0.4, 0.3);
\node[right] at (`r radio_grafico + 1.5`, 0.65) {\small `r categorias[3]`};

\fill[`r colores[4]`!`r intensidades[4]`] (`r radio_grafico + 1`, 0.0) rectangle ++(0.4, 0.3);
\node[right] at (`r radio_grafico + 1.5`, 0.15) {\small `r categorias[4]`};

\end{tikzpicture}

% === CARACTERÍSTICAS DE COMPATIBILIDAD ===
% ✅ Sin bibliotecas adicionales (solo tikz básico)
% ✅ Cálculos simples con variables R
% ✅ Colores estándar con intensidad controlada
% ✅ Sin efectos complejos o transparencias problemáticas
% ✅ Escalable y responsive

% === CASOS DE USO ICFES ===
% - Distribución de datos (Estadística - Nivel 1-2)
% - Análisis de proporciones (Pensamiento Aleatorio)
% - Interpretación de gráficos (Interpretación y Representación)
% - Problemas de porcentajes (Numérico Variacional)
% - Contextos familiares y comunitarios

% === VALIDACIÓN MULTI-FORMATO ===
% ✅ exams2pdf: Excelente
% ✅ exams2html: Excelente
% ✅ exams2moodle: Excelente
% ✅ exams2pandoc: Buena
% ✅ exams2nops: Excelente (colores diferenciables en escala de grises)

% === EJEMPLO DE USO EN .RMD ===
% ```{r generar_grafico_circular, echo=FALSE, results='asis'}
% # Variables del gráfico
% categorias <- c("Fútbol", "Baloncesto", "Tenis", "Natación")
% valores <- c(35, 28, 22, 15)  # Deben sumar 100
% colores <- c("blue", "red", "green", "orange")
% intensidades <- c(60, 60, 60, 60)
% radio_grafico <- 2.5
% mostrar_porcentajes <- TRUE
% mostrar_leyenda <- TRUE
% 
% # Detectar formato
% typ <- match_exams_device()
% ancho_figura <- if(match_exams_call() %in% c("exams2moodle", "exams2qti12")) "8cm" else "10cm"
% 
% # Incluir gráfico
% include_tikz(readLines("grafico-circular-robusto.tikz"),
%              name = "grafico_circular",
%              format = typ,
%              packages = c("tikz"),
%              width = ancho_figura)
% ```

% === VARIANTES DISPONIBLES ===
% 1. Con porcentajes y leyenda (por defecto)
% 2. Solo porcentajes (mostrar_leyenda = FALSE)
% 3. Solo leyenda (mostrar_porcentajes = FALSE)
% 4. Minimalista (ambos FALSE)

% === PERSONALIZACIÓN ===
% - Colores: Usar colores estándar (blue, red, green, orange, purple, brown)
% - Intensidades: 30-80 (30=muy claro, 80=muy oscuro)
% - Radio: 2.0-3.5 (ajustar según espacio disponible)
% - Posición leyenda: Modificar coordenadas base

% === MEJORAS IMPLEMENTADAS ===
% ✅ Cálculos simplificados con variables R
% ✅ Colores adaptativos y accesibles
% ✅ Leyenda posicionada automáticamente
% ✅ Escalado responsive para diferentes formatos
% ✅ Compatible con daltonismo
% ✅ Optimizado para impresión en escala de grises

% === LIMITACIONES CONOCIDAS ===
% - Máximo 6 sectores recomendado para legibilidad
% - Valores deben sumar exactamente 100
% - Requiere al menos 4 categorías (modificar para menos)

% === EXTENSIONES FUTURAS ===
% - Soporte para gráficos de dona (círculo interno)
% - Etiquetas externas con líneas conectoras
% - Animaciones para presentaciones (si se requiere)
